import React, { useState } from 'react';

const CharacterCreation = ({ character: <PERSON><PERSON><PERSON><PERSON>, onCharacterCreated, on<PERSON><PERSON><PERSON> }) => {
    const [character, set<PERSON><PERSON>cter] = useState(editingCharacter || {
        name: '',
        race: '',
        class: '',
        background: '',
        level: 1,
        stats: {
            strength: 10,
            dexterity: 10,
            constitution: 10,
            intelligence: 10,
            wisdom: 10,
            charisma: 10
        }
    });

    const [isSubmitting, setIsSubmitting] = useState(false);
    const [errors, setErrors] = useState({});

    const races = [
        { value: 'human', label: 'Human' },
        { value: 'elf', label: 'Elf' },
        { value: 'dwarf', label: 'Dwarf' },
        { value: 'halfling', label: 'Halfling' },
        { value: 'dragonborn', label: 'Dragonborn' },
        { value: 'gnome', label: 'Gnome' },
        { value: 'half-elf', label: 'Half-Elf' },
        { value: 'half-orc', label: 'Half-Orc' },
        { value: 'tiefling', label: '<PERSON>iefling' }
    ];

    const classes = [
        { value: 'barbarian', label: 'Barbarian' },
        { value: 'bard', label: 'Bard' },
        { value: 'cleric', label: 'Cleric' },
        { value: 'druid', label: 'Druid' },
        { value: 'fighter', label: 'Fighter' },
        { value: 'monk', label: 'Monk' },
        { value: 'paladin', label: 'Paladin' },
        { value: 'ranger', label: 'Ranger' },
        { value: 'rogue', label: 'Rogue' },
        { value: 'sorcerer', label: 'Sorcerer' },
        { value: 'warlock', label: 'Warlock' },
        { value: 'wizard', label: 'Wizard' }
    ];

    const backgrounds = [
        { value: 'acolyte', label: 'Acolyte' },
        { value: 'criminal', label: 'Criminal' },
        { value: 'folk-hero', label: 'Folk Hero' },
        { value: 'noble', label: 'Noble' },
        { value: 'sage', label: 'Sage' },
        { value: 'soldier', label: 'Soldier' },
        { value: 'charlatan', label: 'Charlatan' },
        { value: 'entertainer', label: 'Entertainer' },
        { value: 'guild-artisan', label: 'Guild Artisan' },
        { value: 'hermit', label: 'Hermit' },
        { value: 'outlander', label: 'Outlander' },
        { value: 'sailor', label: 'Sailor' }
    ];

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setCharacter(prev => ({
            ...prev,
            [name]: value
        }));
        
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    const handleStatChange = (stat, value) => {
        const numValue = parseInt(value);
        if (numValue >= 8 && numValue <= 18) {
            setCharacter(prev => ({
                ...prev,
                stats: {
                    ...prev.stats,
                    [stat]: numValue
                }
            }));
        }
    };

    const rollStats = () => {
        const rollStat = () => {
            // Roll 4d6, drop lowest
            const rolls = Array.from({ length: 4 }, () => Math.floor(Math.random() * 6) + 1);
            rolls.sort((a, b) => b - a);
            return rolls.slice(0, 3).reduce((sum, roll) => sum + roll, 0);
        };

        setCharacter(prev => ({
            ...prev,
            stats: {
                strength: rollStat(),
                dexterity: rollStat(),
                constitution: rollStat(),
                intelligence: rollStat(),
                wisdom: rollStat(),
                charisma: rollStat()
            }
        }));
    };

    const validateForm = () => {
        const newErrors = {};

        if (!character.name.trim()) {
            newErrors.name = 'Character name is required';
        }

        if (!character.race) {
            newErrors.race = 'Please select a race';
        }

        if (!character.class) {
            newErrors.class = 'Please select a class';
        }

        if (!character.background) {
            newErrors.background = 'Please select a background';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setIsSubmitting(true);
        
        try {
            const isEditing = editingCharacter && editingCharacter.id;
            const url = isEditing ? `/api/characters/${editingCharacter.id}` : '/api/characters';
            const method = isEditing ? 'put' : 'post';

            const response = await window.axios[method](url, character);

            if (response.data.success) {
                onCharacterCreated(response.data.character);
            } else {
                setErrors({ submit: `Failed to ${isEditing ? 'update' : 'create'} character. Please try again.` });
            }
        } catch (error) {
            console.error(`Error ${editingCharacter ? 'updating' : 'creating'} character:`, error);
            if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            } else {
                setErrors({ submit: 'An error occurred. Please try again.' });
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    const getStatModifier = (stat) => {
        return Math.floor((stat - 10) / 2);
    };

    const formatModifier = (modifier) => {
        return modifier >= 0 ? `+${modifier}` : `${modifier}`;
    };

    return (
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-3xl font-bold text-gray-800">
                    {editingCharacter ? 'Edit Character' : 'Create New Character'}
                </h2>
                <button
                    onClick={onCancel}
                    className="text-gray-500 hover:text-gray-700 text-xl font-bold"
                >
                    ×
                </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Character Name *
                        </label>
                        <input
                            type="text"
                            name="name"
                            value={character.name}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                                errors.name ? 'border-red-500' : 'border-gray-300'
                            }`}
                            placeholder="Enter character name"
                        />
                        {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Level
                        </label>
                        <input
                            type="number"
                            name="level"
                            value={character.level}
                            onChange={handleInputChange}
                            min="1"
                            max="20"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                    </div>
                </div>

                {/* Race, Class, Background */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Race *
                        </label>
                        <select
                            name="race"
                            value={character.race}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                                errors.race ? 'border-red-500' : 'border-gray-300'
                            }`}
                        >
                            <option value="">Select a race</option>
                            {races.map(race => (
                                <option key={race.value} value={race.value}>
                                    {race.label}
                                </option>
                            ))}
                        </select>
                        {errors.race && <p className="text-red-500 text-sm mt-1">{errors.race}</p>}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Class *
                        </label>
                        <select
                            name="class"
                            value={character.class}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                                errors.class ? 'border-red-500' : 'border-gray-300'
                            }`}
                        >
                            <option value="">Select a class</option>
                            {classes.map(cls => (
                                <option key={cls.value} value={cls.value}>
                                    {cls.label}
                                </option>
                            ))}
                        </select>
                        {errors.class && <p className="text-red-500 text-sm mt-1">{errors.class}</p>}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Background *
                        </label>
                        <select
                            name="background"
                            value={character.background}
                            onChange={handleInputChange}
                            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                                errors.background ? 'border-red-500' : 'border-gray-300'
                            }`}
                        >
                            <option value="">Select a background</option>
                            {backgrounds.map(bg => (
                                <option key={bg.value} value={bg.value}>
                                    {bg.label}
                                </option>
                            ))}
                        </select>
                        {errors.background && <p className="text-red-500 text-sm mt-1">{errors.background}</p>}
                    </div>
                </div>

                {/* Ability Scores */}
                <div>
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-semibold text-gray-800">Ability Scores</h3>
                        <button
                            type="button"
                            onClick={rollStats}
                            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                        >
                            🎲 Roll Stats
                        </button>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                        {Object.entries(character.stats).map(([stat, value]) => (
                            <div key={stat} className="text-center">
                                <label className="block text-sm font-medium text-gray-700 mb-1 capitalize">
                                    {stat}
                                </label>
                                <input
                                    type="number"
                                    min="8"
                                    max="18"
                                    value={value}
                                    onChange={(e) => handleStatChange(stat, e.target.value)}
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                                <div className="text-xs text-gray-500 mt-1">
                                    {formatModifier(getStatModifier(value))}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Error Messages */}
                {errors.submit && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4">
                        <p className="text-red-600">{errors.submit}</p>
                    </div>
                )}

                {/* Submit Buttons */}
                <div className="flex justify-end space-x-4 pt-6">
                    <button
                        type="button"
                        onClick={onCancel}
                        className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        disabled={isSubmitting}
                        className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isSubmitting
                            ? (editingCharacter ? 'Updating...' : 'Creating...')
                            : (editingCharacter ? 'Update Character' : 'Create Character')
                        }
                    </button>
                </div>
            </form>
        </div>
    );
};

export default CharacterCreation;
