<?php return array(
    'root' => array(
        'pretty_version' => 'v12.2.0',
        'version' => '12.2.0.0',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => null,
        'name' => 'laravel/laravel',
        'dev' => true,
    ),
    'versions' => array(
        'brick/math' => array(
            'pretty_version' => '0.13.1',
            'version' => '0.13.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'reference' => 'fc7ed316430118cc7836bf45faff18d5dfc8de04',
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'reference' => '18ba5ddfec8976260ead6e866180bd5d2f71aa1d',
            'dev_requirement' => false,
        ),
        'cordoval/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'davedevelopment/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'reference' => 'a23a2bf4f31d3518f3ecb38660c95715dfead60f',
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'reference' => '31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd',
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v3.4.0',
            'version' => '3.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'reference' => '8c784d071debd117328803d86b2097615b457500',
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'reference' => 'd42c8731f0624ad6bdc8d3e5e9a4524f68801cfa',
            'dev_requirement' => false,
        ),
        'fakerphp/faker' => array(
            'pretty_version' => 'v1.24.1',
            'version' => '1.24.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fakerphp/faker',
            'aliases' => array(),
            'reference' => 'e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5',
            'dev_requirement' => true,
        ),
        'filp/whoops' => array(
            'pretty_version' => '2.18.3',
            'version' => '2.18.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filp/whoops',
            'aliases' => array(),
            'reference' => '59a123a3d459c5a23055802237cb317f609867e5',
            'dev_requirement' => true,
        ),
        'fruitcake/php-cors' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fruitcake/php-cors',
            'aliases' => array(),
            'reference' => '3d158f36e7875e2f040f37bc0573956240a5a38b',
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'dev_requirement' => false,
        ),
        'guzzlehttp/uri-template' => array(
            'pretty_version' => 'v1.0.4',
            'version' => '1.0.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/uri-template',
            'aliases' => array(),
            'reference' => '30e286560c137526eccd4ce21b2de477ab0676d2',
            'dev_requirement' => false,
        ),
        'hamcrest/hamcrest-php' => array(
            'pretty_version' => 'v2.1.1',
            'version' => '2.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hamcrest/hamcrest-php',
            'aliases' => array(),
            'reference' => 'f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487',
            'dev_requirement' => true,
        ),
        'illuminate/auth' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/broadcasting' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/bus' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/collections' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/concurrency' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/conditionable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/config' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/container' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/contracts' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/cookie' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/events' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/hashing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/macroable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/mail' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/notifications' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/pagination' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/pipeline' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/process' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/queue' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/redis' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/routing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/session' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/support' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/testing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/translation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'illuminate/view' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v12.21.0',
            ),
        ),
        'kodova/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'laravel/framework' => array(
            'pretty_version' => 'v12.21.0',
            'version' => '12.21.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/framework',
            'aliases' => array(),
            'reference' => 'ac8c4e73bf1b5387b709f7736d41427e6af1c93b',
            'dev_requirement' => false,
        ),
        'laravel/laravel' => array(
            'pretty_version' => 'v12.2.0',
            'version' => '12.2.0.0',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => null,
            'dev_requirement' => false,
        ),
        'laravel/pail' => array(
            'pretty_version' => 'v1.2.3',
            'version' => '1.2.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/pail',
            'aliases' => array(),
            'reference' => '8cc3d575c1f0e57eeb923f366a37528c50d2385a',
            'dev_requirement' => true,
        ),
        'laravel/pint' => array(
            'pretty_version' => 'v1.24.0',
            'version' => '1.24.0.0',
            'type' => 'project',
            'install_path' => __DIR__ . '/../laravel/pint',
            'aliases' => array(),
            'reference' => '0345f3b05f136801af8c339f9d16ef29e6b4df8a',
            'dev_requirement' => true,
        ),
        'laravel/prompts' => array(
            'pretty_version' => 'v0.3.6',
            'version' => '0.3.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/prompts',
            'aliases' => array(),
            'reference' => '86a8b692e8661d0fb308cec64f3d176821323077',
            'dev_requirement' => false,
        ),
        'laravel/sail' => array(
            'pretty_version' => 'v1.44.0',
            'version' => '1.44.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sail',
            'aliases' => array(),
            'reference' => 'a09097bd2a8a38e23ac472fa6a6cf5b0d1c1d3fe',
            'dev_requirement' => true,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v2.0.4',
            'version' => '2.0.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'reference' => 'b352cf0534aa1ae6b4d825d1e762e35d43f8a841',
            'dev_requirement' => false,
        ),
        'laravel/tinker' => array(
            'pretty_version' => 'v2.10.1',
            'version' => '2.10.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/tinker',
            'aliases' => array(),
            'reference' => '22177cc71807d38f2810c6204d8f7183d88a57d3',
            'dev_requirement' => false,
        ),
        'league/commonmark' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'reference' => '10732241927d3971d28e7ea7b5712721fa2296ca',
            'dev_requirement' => false,
        ),
        'league/config' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/config',
            'aliases' => array(),
            'reference' => '754b3604fb2984c71f4af4a9cbe7b57f346ec1f3',
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.30.0',
            'version' => '3.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'reference' => '2203e3151755d874bb2943649dae1eb8533ac93e',
            'dev_requirement' => false,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.30.0',
            'version' => '3.30.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'reference' => '6691915f77c7fb69adfb87dcd550052dc184ee10',
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.16.0',
            'version' => '1.16.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'reference' => '2d6702ff215bf922936ccc1ad31007edc76451b9',
            'dev_requirement' => false,
        ),
        'league/uri' => array(
            'pretty_version' => '7.5.1',
            'version' => '7.5.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri',
            'aliases' => array(),
            'reference' => '81fb5145d2644324614cc532b28efd0215bda430',
            'dev_requirement' => false,
        ),
        'league/uri-interfaces' => array(
            'pretty_version' => '7.5.0',
            'version' => '7.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/uri-interfaces',
            'aliases' => array(),
            'reference' => '08cfc6c4f3d811584fb09c37e2849e6a7f9b0742',
            'dev_requirement' => false,
        ),
        'mockery/mockery' => array(
            'pretty_version' => '1.6.12',
            'version' => '1.6.12.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mockery/mockery',
            'aliases' => array(),
            'reference' => '1f4efdd7d3beafe9807b08156dfcb176d18f1699',
            'dev_requirement' => true,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.9.0',
            'version' => '3.9.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'reference' => '10d85740180ecba7896c87e06a166e0c95a0e3b6',
            'dev_requirement' => false,
        ),
        'mtdowling/cron-expression' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.0',
            ),
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.13.3',
            'version' => '1.13.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'reference' => 'faed855a7b5f4d4637717c2b3863e277116beb36',
            'dev_requirement' => true,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '3.10.1',
            'version' => '3.10.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'reference' => '1fd1935b2d90aef2f093c5e35f7ae1257c448d00',
            'dev_requirement' => false,
        ),
        'nette/schema' => array(
            'pretty_version' => 'v1.3.2',
            'version' => '1.3.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/schema',
            'aliases' => array(),
            'reference' => 'da801d52f0354f70a638673c4a0f04e16529431d',
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.7',
            'version' => '4.0.7.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'reference' => 'e67c4061eb40b9c113b218214e42cb5a0dda28f2',
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.5.0',
            'version' => '5.5.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'reference' => 'ae59794362fe85e051a58ad36b289443f57be7a9',
            'dev_requirement' => false,
        ),
        'nunomaduro/collision' => array(
            'pretty_version' => 'v8.8.2',
            'version' => '8.8.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/collision',
            'aliases' => array(),
            'reference' => '60207965f9b7b7a4ce15a0f75d57f9dadb105bdb',
            'dev_requirement' => true,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v2.3.1',
            'version' => '2.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'reference' => 'dfa08f390e509967a15c22493dc0bac5733d9123',
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'reference' => '54750ef60c58e43759730615a392c31c80e23176',
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'dev_requirement' => true,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '1.9.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'dev_requirement' => false,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '11.0.10',
            'version' => '11.0.10.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'reference' => '1a800a7446add2d79cc6b3c01c45381810367d76',
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '5.1.0',
            'version' => '5.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'reference' => '118cfaaa8bc5aef3287bf315b6060b1174754af6',
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '5.0.1',
            'version' => '5.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'reference' => 'c1ca3814734c07492b3d4c5f794f4b0995333da2',
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'reference' => '3e0404dc6b300e6bf56415467ebcb3fe4f33e964',
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '7.0.1',
            'version' => '7.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'reference' => '3b415def83fbcb41f991d9ebf16ae4ad8b7837b3',
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '11.5.27',
            'version' => '11.5.27.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'reference' => '446d43867314781df7e9adf79c3ec7464956fd8f',
            'dev_requirement' => true,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
                1 => '3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.12.9',
            'version' => '0.12.9.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'reference' => '1b801844becfe648985372cb4b12ad6840245ace',
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'reference' => '344572933ad0181accbf4ba763e85a0306a8c5e2',
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.9.0',
            'version' => '4.9.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'reference' => '4e0e23cc785f0724a0e838279a9eb03f28b092a0',
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.9.0',
            ),
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'reference' => '15c5dd40dc4f38794d383bb95465193f5e0ae180',
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'reference' => '54391c61e4af8078e5b276ab082b6d3c54c9ad64',
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'reference' => '183a9b2632194febd219bb9246eee421dad8d45e',
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '6.3.1',
            'version' => '6.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'reference' => '24b8fbc2c8e201bb1308e7b05148d6ab393b6959',
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'reference' => 'ee41d384ab1906c68852636b6de493846e13e5a0',
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'reference' => 'b4ccd857127db5d41a5b676f24b51371d76d8544',
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '7.2.1',
            'version' => '7.2.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'reference' => 'a5c75038693ad2e8d4b6c15ba2403532647830c4',
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '6.3.0',
            'version' => '6.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'reference' => '3473f61172093b2da7de1fb5782e1f24cc036dc3',
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '7.0.2',
            'version' => '7.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'reference' => '3be331570a721f9a4b5917f4209773de17f747d7',
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'reference' => 'd36ad0d782e5756913e42ad87cb2890f4ffe467a',
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '6.0.1',
            'version' => '6.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'reference' => 'f5b498e631a74204185071eb41f33f38d64608aa',
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '4.0.1',
            'version' => '4.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'reference' => '6e1a43b411b2ad34146dee7524cb13a068bb35f9',
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '6.0.2',
            'version' => '6.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'reference' => '694d156164372abbd149a4b85ccda2e4670c0e16',
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '5.1.2',
            'version' => '5.1.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'reference' => 'a8a7e30534b0eb0c77cd9d07e82de1a114389f5e',
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '5.0.2',
            'version' => '5.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'reference' => 'c687e3387b99f5b03b6caa64c74b63e2936ff874',
            'dev_requirement' => true,
        ),
        'spatie/once' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'staabm/side-effects-detector' => array(
            'pretty_version' => '1.0.5',
            'version' => '1.0.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../staabm/side-effects-detector',
            'aliases' => array(),
            'reference' => 'd8334211a140ce329c13726d4a715adbddd0a163',
            'dev_requirement' => true,
        ),
        'symfony/clock' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/clock',
            'aliases' => array(),
            'reference' => 'b81435fbd6648ea425d1ee96a2d8e68f4ceacd24',
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'reference' => '9e27aecde8f506ba0fd1d9989620c04a87697101',
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'reference' => '601a5ce9aaad7bf10797e3663faefce9e26c24e2',
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'reference' => '35b55b166f6752d6aaf21aa042fc5ed280fce235',
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'reference' => '497f73ac996a598c92409b44ac43b6690c4f666d',
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'reference' => '59eb412e93815df44f05f342958efa9f46b1e586',
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'reference' => 'ec2344cf77a48253bbca6939aa3d2477773ea63d',
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'reference' => '23dd60256610c86a3414575b70c596e5deff6ed9',
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'reference' => '1644879a66e4aa29c36fe33dfa6c54b450ce1831',
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'reference' => 'b5db5105b290bdbea5ab27b89c69effcf1cb3368',
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'reference' => '0e7b19b2f399c31df0cdbe5d8cbf53f02f6cfcd9',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'reference' => '9614ac4d8061dc257ecc64cba1b140873dce8ad3',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-uuid' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-uuid',
            'aliases' => array(),
            'reference' => '21533be36c24be3f4b1669c4725c7d1d2bab4ae2',
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'reference' => '40c295f2deb408d5e9d2d32b8ba1dd61e36f05af',
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'reference' => '8e213820c5fea844ecea29203d2a308019007c15',
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v7.3.0',
            'version' => '7.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'reference' => 'f3570b8c61ca887a9e2938e85cb6458515d2b125',
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'reference' => '241d5ac4910d256660238a7ecf250deba4c73063',
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'reference' => 'df210c7a2573f1913b2d17cc95f90f53a73d8f7d',
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/uid' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/uid',
            'aliases' => array(),
            'reference' => 'a69f69f3159b852651a6bf45a9fdd149520525bb',
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'reference' => '6e209fbe5f5a7b6043baba46fe5735a4b85d0d42',
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v7.3.1',
            'version' => '7.3.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'reference' => '0c3555045a46ab3cd4cc5a69d161225195230edb',
            'dev_requirement' => true,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'reference' => '737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2',
            'dev_requirement' => true,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '2.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'reference' => '0d72ac1c00084279c1816675284073c5a337c20d',
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.2',
            'version' => '5.6.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'reference' => '24ac4c74f91ee2c193fa1aaa5c249cb0822809af',
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'reference' => 'b1d923f88091c6bf09699efcd7c8a1b1bfd7351d',
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'dev_requirement' => false,
        ),
    ),
);
