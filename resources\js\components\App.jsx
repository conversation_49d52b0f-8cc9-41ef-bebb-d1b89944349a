import React, { useState } from 'react';
import CharacterList from './CharacterList';
import CharacterCreation from './CharacterCreation';

function App() {
    const [currentView, setCurrentView] = useState('home'); // 'home', 'characters', 'create-character'
    const [editing<PERSON>haracter, setEditingCharacter] = useState(null);

    const handleCreateCharacter = () => {
        setEditingCharacter(null);
        setCurrentView('create-character');
    };

    const handleEditCharacter = (character) => {
        setEditingCharacter(character);
        setCurrentView('create-character');
    };

    const handleCharacterCreated = () => {
        setCurrentView('characters');
        setEditingCharacter(null);
    };

    const handleCancel = () => {
        setCurrentView(currentView === 'create-character' ? 'characters' : 'home');
        setEditingCharacter(null);
    };

    const renderContent = () => {
        switch (currentView) {
            case 'characters':
                return (
                    <CharacterList
                        onEditCharacter={handleEditCharacter}
                        onCreateNew={handleCreate<PERSON>haracter}
                    />
                );
            case 'create-character':
                return (
                    <CharacterCreation
                        character={editingCharacter}
                        onCharacterCreated={handleCharacterCreated}
                        onCancel={handleCancel}
                    />
                );
            default:
                return (
                    <div className="max-w-4xl mx-auto">
                        <div className="bg-white rounded-lg shadow-md p-6">
                            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                                Welcome to your RPG Application!
                            </h2>
                            <p className="text-gray-600 mb-4">
                                This is your React component running inside a Laravel application.
                                You can now start building your RPG features!
                            </p>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                                <button
                                    onClick={() => setCurrentView('characters')}
                                    className="bg-blue-50 p-4 rounded-lg hover:bg-blue-100 transition-colors text-left"
                                >
                                    <h3 className="font-semibold text-blue-800 mb-2">Characters</h3>
                                    <p className="text-blue-600 text-sm">
                                        Create and manage your RPG characters
                                    </p>
                                </button>

                                <div className="bg-green-50 p-4 rounded-lg opacity-50 cursor-not-allowed">
                                    <h3 className="font-semibold text-green-800 mb-2">Quests</h3>
                                    <p className="text-green-600 text-sm">
                                        Design exciting quests and adventures (Coming Soon)
                                    </p>
                                </div>

                                <div className="bg-purple-50 p-4 rounded-lg opacity-50 cursor-not-allowed">
                                    <h3 className="font-semibold text-purple-800 mb-2">Inventory</h3>
                                    <p className="text-purple-600 text-sm">
                                        Manage items, weapons, and equipment (Coming Soon)
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                );
        }
    };

    return (
        <div className="min-h-screen bg-gray-100">
            <div className="container mx-auto px-4 py-8">
                <header className="text-center mb-8">
                    <h1
                        className="text-4xl font-bold text-gray-800 mb-2 cursor-pointer hover:text-blue-600 transition-colors"
                        onClick={() => setCurrentView('home')}
                    >
                        RPG Test Application
                    </h1>
                    <p className="text-gray-600">
                        Laravel + React + Tailwind CSS
                    </p>

                    {/* Navigation */}
                    <nav className="mt-6">
                        <div className="flex justify-center space-x-6">
                            <button
                                onClick={() => setCurrentView('home')}
                                className={`px-4 py-2 rounded-md transition-colors ${
                                    currentView === 'home'
                                        ? 'bg-blue-600 text-white'
                                        : 'text-gray-600 hover:text-blue-600'
                                }`}
                            >
                                Home
                            </button>
                            <button
                                onClick={() => setCurrentView('characters')}
                                className={`px-4 py-2 rounded-md transition-colors ${
                                    currentView === 'characters' || currentView === 'create-character'
                                        ? 'bg-blue-600 text-white'
                                        : 'text-gray-600 hover:text-blue-600'
                                }`}
                            >
                                Characters
                            </button>
                        </div>
                    </nav>
                </header>

                <main>
                    {renderContent()}
                </main>
            </div>
        </div>
    );
}

export default App;
