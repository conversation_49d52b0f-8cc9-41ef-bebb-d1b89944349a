import React from 'react';

function App() {
    return (
        <div className="min-h-screen bg-gray-100">
            <div className="container mx-auto px-4 py-8">
                <header className="text-center mb-8">
                    <h1 className="text-4xl font-bold text-gray-800 mb-2">
                        RPG Test Application
                    </h1>
                    <p className="text-gray-600">
                        Laravel + React + Tailwind CSS
                    </p>
                </header>
                
                <main className="max-w-4xl mx-auto">
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                            Welcome to your RPG Application!
                        </h2>
                        <p className="text-gray-600 mb-4">
                            This is your React component running inside a Laravel application.
                            You can now start building your RPG features!
                        </p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                            <div className="bg-blue-50 p-4 rounded-lg">
                                <h3 className="font-semibold text-blue-800 mb-2">Characters</h3>
                                <p className="text-blue-600 text-sm">
                                    Create and manage your RPG characters
                                </p>
                            </div>
                            
                            <div className="bg-green-50 p-4 rounded-lg">
                                <h3 className="font-semibold text-green-800 mb-2">Quests</h3>
                                <p className="text-green-600 text-sm">
                                    Design exciting quests and adventures
                                </p>
                            </div>
                            
                            <div className="bg-purple-50 p-4 rounded-lg">
                                <h3 className="font-semibold text-purple-800 mb-2">Inventory</h3>
                                <p className="text-purple-600 text-sm">
                                    Manage items, weapons, and equipment
                                </p>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    );
}

export default App;
