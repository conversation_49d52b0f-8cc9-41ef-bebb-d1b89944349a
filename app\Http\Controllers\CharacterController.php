<?php

namespace App\Http\Controllers;

use App\Models\Character;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;

class CharacterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $characters = Character::orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'characters' => $characters
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'race' => ['required', 'string', Rule::in([
                'human', 'elf', 'dwarf', 'halfling', 'dragonborn',
                'gnome', 'half-elf', 'half-orc', 'tiefling'
            ])],
            'class' => ['required', 'string', Rule::in([
                'barbarian', 'bard', 'cleric', 'druid', 'fighter',
                'monk', 'paladin', 'ranger', 'rogue', 'sorcerer',
                'warlock', 'wizard'
            ])],
            'background' => ['required', 'string', Rule::in([
                'acolyte', 'criminal', 'folk-hero', 'noble', 'sage',
                'soldier', 'charlatan', 'entertainer', 'guild-artisan',
                'hermit', 'outlander', 'sailor'
            ])],
            'level' => 'required|integer|min:1|max:20',
            'stats' => 'required|array',
            'stats.strength' => 'required|integer|min:8|max:18',
            'stats.dexterity' => 'required|integer|min:8|max:18',
            'stats.constitution' => 'required|integer|min:8|max:18',
            'stats.intelligence' => 'required|integer|min:8|max:18',
            'stats.wisdom' => 'required|integer|min:8|max:18',
            'stats.charisma' => 'required|integer|min:8|max:18',
        ]);

        $character = Character::create($validatedData);

        return response()->json([
            'success' => true,
            'character' => $character,
            'message' => 'Character created successfully!'
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $character = Character::findOrFail($id);

        return response()->json([
            'success' => true,
            'character' => $character
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $character = Character::findOrFail($id);

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'race' => ['required', 'string', Rule::in([
                'human', 'elf', 'dwarf', 'halfling', 'dragonborn',
                'gnome', 'half-elf', 'half-orc', 'tiefling'
            ])],
            'class' => ['required', 'string', Rule::in([
                'barbarian', 'bard', 'cleric', 'druid', 'fighter',
                'monk', 'paladin', 'ranger', 'rogue', 'sorcerer',
                'warlock', 'wizard'
            ])],
            'background' => ['required', 'string', Rule::in([
                'acolyte', 'criminal', 'folk-hero', 'noble', 'sage',
                'soldier', 'charlatan', 'entertainer', 'guild-artisan',
                'hermit', 'outlander', 'sailor'
            ])],
            'level' => 'required|integer|min:1|max:20',
            'stats' => 'required|array',
            'stats.strength' => 'required|integer|min:8|max:18',
            'stats.dexterity' => 'required|integer|min:8|max:18',
            'stats.constitution' => 'required|integer|min:8|max:18',
            'stats.intelligence' => 'required|integer|min:8|max:18',
            'stats.wisdom' => 'required|integer|min:8|max:18',
            'stats.charisma' => 'required|integer|min:8|max:18',
        ]);

        $character->update($validatedData);

        return response()->json([
            'success' => true,
            'character' => $character,
            'message' => 'Character updated successfully!'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $character = Character::findOrFail($id);
        $character->delete();

        return response()->json([
            'success' => true,
            'message' => 'Character deleted successfully!'
        ]);
    }
}
