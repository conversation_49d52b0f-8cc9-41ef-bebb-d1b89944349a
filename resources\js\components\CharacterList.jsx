import React, { useState, useEffect } from 'react';

const CharacterList = ({ onEditCharacter, onCreateNew }) => {
    const [characters, setCharacters] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        fetchCharacters();
    }, []);

    const fetchCharacters = async () => {
        try {
            setLoading(true);
            const response = await window.axios.get('/api/characters');
            setCharacters(response.data.characters || []);
        } catch (error) {
            console.error('Error fetching characters:', error);
            setError('Failed to load characters');
        } finally {
            setLoading(false);
        }
    };

    const deleteCharacter = async (characterId) => {
        if (!confirm('Are you sure you want to delete this character?')) {
            return;
        }

        try {
            await window.axios.delete(`/api/characters/${characterId}`);
            setCharacters(prev => prev.filter(char => char.id !== characterId));
        } catch (error) {
            console.error('Error deleting character:', error);
            alert('Failed to delete character');
        }
    };

    const getStatModifier = (stat) => {
        return Math.floor((stat - 10) / 2);
    };

    const formatModifier = (modifier) => {
        return modifier >= 0 ? `+${modifier}` : `${modifier}`;
    };

    const capitalizeFirst = (str) => {
        return str.charAt(0).toUpperCase() + str.slice(1);
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-red-600">{error}</p>
                <button
                    onClick={fetchCharacters}
                    className="mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                >
                    Retry
                </button>
            </div>
        );
    }

    return (
        <div className="max-w-6xl mx-auto">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-3xl font-bold text-gray-800">Your Characters</h2>
                <button
                    onClick={onCreateNew}
                    className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                    + Create New Character
                </button>
            </div>

            {characters.length === 0 ? (
                <div className="text-center py-12">
                    <div className="text-gray-400 text-6xl mb-4">⚔️</div>
                    <h3 className="text-xl font-semibold text-gray-600 mb-2">No Characters Yet</h3>
                    <p className="text-gray-500 mb-6">Create your first character to begin your adventure!</p>
                    <button
                        onClick={onCreateNew}
                        className="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        Create Your First Character
                    </button>
                </div>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {characters.map(character => (
                        <div key={character.id} className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                            {/* Character Header */}
                            <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4">
                                <h3 className="text-xl font-bold">{character.name}</h3>
                                <p className="text-blue-100">
                                    Level {character.level} {capitalizeFirst(character.race)} {capitalizeFirst(character.class)}
                                </p>
                            </div>

                            {/* Character Details */}
                            <div className="p-4">
                                <div className="mb-4">
                                    <p className="text-sm text-gray-600">
                                        <span className="font-medium">Background:</span> {capitalizeFirst(character.background)}
                                    </p>
                                </div>

                                {/* Ability Scores */}
                                <div className="mb-4">
                                    <h4 className="text-sm font-semibold text-gray-700 mb-2">Ability Scores</h4>
                                    <div className="grid grid-cols-3 gap-2 text-xs">
                                        {character.stats && Object.entries(character.stats).map(([stat, value]) => (
                                            <div key={stat} className="text-center bg-gray-50 rounded p-1">
                                                <div className="font-medium text-gray-700 capitalize">
                                                    {stat.slice(0, 3)}
                                                </div>
                                                <div className="text-lg font-bold text-gray-800">{value}</div>
                                                <div className="text-gray-500">
                                                    {formatModifier(getStatModifier(value))}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex space-x-2">
                                    <button
                                        onClick={() => onEditCharacter(character)}
                                        className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        onClick={() => deleteCharacter(character.id)}
                                        className="px-3 py-2 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                                    >
                                        Delete
                                    </button>
                                </div>
                            </div>

                            {/* Character Creation Date */}
                            <div className="px-4 py-2 bg-gray-50 border-t border-gray-200">
                                <p className="text-xs text-gray-500">
                                    Created: {new Date(character.created_at).toLocaleDateString()}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

export default CharacterList;
